<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe"/>
      <stop offset="100%" style="stop-color:#00f2fe"/>
    </linearGradient>
    <linearGradient id="clock" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea"/>
      <stop offset="100%" style="stop-color:#764ba2"/>
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#bg)" stroke="white" stroke-width="4"/>
  
  <!-- Clock face -->
  <circle cx="64" cy="64" r="35" fill="white" stroke="url(#clock)" stroke-width="3"/>
  
  <!-- Clock hands -->
  <line x1="64" y1="64" x2="64" y2="40" stroke="url(#clock)" stroke-width="4" stroke-linecap="round"/>
  <line x1="64" y1="64" x2="80" y2="64" stroke="url(#clock)" stroke-width="3" stroke-linecap="round"/>
  
  <!-- Center dot -->
  <circle cx="64" cy="64" r="3" fill="url(#clock)"/>
  
  <!-- Time markers -->
  <circle cx="64" cy="35" r="2" fill="url(#clock)"/>
  <circle cx="93" cy="64" r="2" fill="url(#clock)"/>
  <circle cx="64" cy="93" r="2" fill="url(#clock)"/>
  <circle cx="35" cy="64" r="2" fill="url(#clock)"/>
  
  <!-- Shield overlay for "safe" concept -->
  <path d="M64 20 L80 30 L80 50 C80 65 72 75 64 80 C56 75 48 65 48 50 L48 30 Z" 
        fill="rgba(255,255,255,0.3)" stroke="white" stroke-width="2"/>
</svg>
