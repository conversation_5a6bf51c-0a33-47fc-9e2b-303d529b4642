let overlay;
let lastActivityTime = Date.now();
let isActive = false;

function createOverlay(siteName = 'this website') {
    if (overlay) return; // Prevent creating multiple overlays
    overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(0,0,0,0.95) 0%, rgba(30,30,30,0.98) 100%);
        z-index: 999999;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        color: white;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        backdrop-filter: blur(10px);
    `;

    overlay.innerHTML = `
        <div style="text-align: center; max-width: 500px; padding: 40px;">
            <div style="font-size: 80px; margin-bottom: 20px;">⏰</div>
            <h1 style="font-size: 32px; margin-bottom: 15px; font-weight: 600;">Time's Up!</h1>
            <p style="font-size: 18px; margin-bottom: 30px; opacity: 0.9; line-height: 1.5;">
                You've reached your daily limit for <strong>${siteName}</strong>.<br>
                It's time to take a break and focus on other activities.
            </p>
            <button id="leaveButton" style="
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
                border: none;
                padding: 15px 30px;
                font-size: 18px;
                border-radius: 10px;
                cursor: pointer;
                font-weight: 600;
                box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
                transition: all 0.3s ease;
            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(79, 172, 254, 0.4)'"
               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(79, 172, 254, 0.3)'">
                Leave ${siteName}
            </button>
        </div>
    `;

    document.body.appendChild(overlay);

    document.getElementById('leaveButton').addEventListener('click', () => {
        chrome.runtime.sendMessage({ action: "leaveWebsite" });
    });
}

function updateActiveState() {
    const currentTime = Date.now();
    if (currentTime - lastActivityTime < 60000) { // 1 minute of inactivity
        if (!isActive) {
            isActive = true;
            chrome.runtime.sendMessage({ action: "setActive", isActive: true });
        }
    } else {
        if (isActive) {
            isActive = false;
            chrome.runtime.sendMessage({ action: "setActive", isActive: false });
        }
    }
}

// Event listeners for user activity
['mousemove', 'keydown', 'scroll', 'click'].forEach(eventType => {
    document.addEventListener(eventType, () => {
        lastActivityTime = Date.now();
        updateActiveState();
    });
});

// Start updating active state
setInterval(updateActiveState, 60000); // Check activity every minute

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "showOverlay") {
        createOverlay(request.siteName || 'this website');
    }
});

// Notify background script when the page is loaded
chrome.runtime.sendMessage({ action: "pageLoaded" });