# Facebook Usage Limiter Chrome Extension

## Overview

This Chrome extension was created as a fun project to hack around with for a couple of hours, aimed at eradicating time waste on social media, specifically Facebook. It helps users manage their time on the platform by setting daily usage limits and providing visual feedback on time spent.

## Features

- Set custom daily time limits for Facebook usage
- Track time spent on Facebook
- Display remaining time for the day
- Show an overlay when the time limit is reached
- Automatically reset usage statistics daily

## Installation

To install this extension locally, follow these steps:

1. Clone this repository or download the source code as a ZIP file and extract it.

2. Open Google Chrome and navigate to `chrome://extensions/`.

3. Enable "Developer mode" by toggling the switch in the top right corner.

4. Click on "Load unpacked" button that appears after enabling Developer mode.

5. Select the directory containing the extension files (manifest.json, background.js, content.js, popup.html, popup.js).

6. The extension should now be installed and visible in your Chrome browser.

## Usage

1. Click on the extension icon in the Chrome toolbar to open the popup.

2. Set your desired daily time limit in minutes.

3. Browse Facebook as usual. The extension will track your active time on the site.

4. When you reach your set time limit, an overlay will appear, prompting you to leave Facebook.

5. You can view your daily usage and remaining time by clicking on the extension icon.

## Contributing

This was a quick project, but contributions are welcome! Feel free to fork the repository and submit pull requests with improvements or new features.

## Disclaimer

This extension is for personal use and educational purposes only. It is not affiliated with or endorsed by Facebook.

Enjoy using Facebook more mindfully!
