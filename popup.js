document.addEventListener('DOMContentLoaded', () => {
    const timeSpentElement = document.getElementById('timeSpent');
    const timeRemainingElement = document.getElementById('timeRemaining');
    const timeLimitInput = document.getElementById('timeLimit');
    const saveButton = document.getElementById('save');
    const progressFill = document.getElementById('progressFill');
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');
    const quickButtons = document.querySelectorAll('.quick-btn');

    let currentTimeLimit = 30; // Default 30 minutes
    let currentTimeSpent = 0;

    // Load current values
    chrome.storage.sync.get(['timeLimit', 'timeSpent'], (result) => {
        currentTimeLimit = result.timeLimit || 30;
        currentTimeSpent = result.timeSpent || 0;
        timeLimitInput.value = currentTimeLimit;
        updateDisplay();
        updateQuickButtonsState();
    });

    // Quick time buttons
    quickButtons.forEach(button => {
        button.addEventListener('click', () => {
            const time = parseInt(button.dataset.time);
            timeLimitInput.value = time;
            currentTimeLimit = time;
            updateQuickButtonsState();
            updateDisplay();
        });
    });

    // Update quick buttons visual state
    function updateQuickButtonsState() {
        quickButtons.forEach(button => {
            const time = parseInt(button.dataset.time);
            if (time === currentTimeLimit) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    }

    // Input change handler
    timeLimitInput.addEventListener('input', () => {
        const newLimit = parseInt(timeLimitInput.value);
        if (newLimit > 0) {
            currentTimeLimit = newLimit;
            updateDisplay();
            updateQuickButtonsState();
        }
    });

    // Save new time limit
    saveButton.addEventListener('click', () => {
        const newLimit = parseInt(timeLimitInput.value);
        if (newLimit > 0 && newLimit <= 1440) { // Max 24 hours
            chrome.storage.sync.set({ timeLimit: newLimit }, () => {
                showNotification('Time limit updated successfully!', 'success');
                currentTimeLimit = newLimit;
                updateDisplay();
            });
        } else {
            showNotification('Please enter a valid time limit (1-1440 minutes)', 'error');
        }
    });

    // Show notification
    function showNotification(message, type) {
        // Create a temporary notification element
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                to { opacity: 1; transform: translateX(-50%) translateY(0); }
            }
        `;
        document.head.appendChild(style);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
            style.remove();
        }, 3000);
    }

    // Format time as MM:SS
    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    // Update all display elements
    function updateDisplay() {
        const spentSeconds = currentTimeSpent;
        const limitSeconds = currentTimeLimit * 60;
        const remainingSeconds = Math.max(0, limitSeconds - spentSeconds);

        // Update time displays
        timeSpentElement.textContent = formatTime(spentSeconds);
        timeRemainingElement.textContent = formatTime(remainingSeconds);

        // Update progress bar
        const progressPercentage = Math.min(100, (spentSeconds / limitSeconds) * 100);
        progressFill.style.width = `${progressPercentage}%`;

        // Update status indicator
        updateStatusIndicator(progressPercentage, remainingSeconds);

        // Update progress bar color based on usage
        if (progressPercentage >= 90) {
            progressFill.style.background = 'linear-gradient(90deg, #ff6b6b 0%, #ee5a52 100%)';
        } else if (progressPercentage >= 75) {
            progressFill.style.background = 'linear-gradient(90deg, #ffa726 0%, #ff9800 100%)';
        } else {
            progressFill.style.background = 'linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)';
        }
    }

    // Update status indicator
    function updateStatusIndicator(progressPercentage, remainingSeconds) {
        let statusClass, message;

        if (progressPercentage >= 100) {
            statusClass = 'status-danger';
            message = 'Time limit exceeded! Take a break.';
        } else if (progressPercentage >= 90) {
            statusClass = 'status-danger';
            message = 'Almost out of time! Wrap up soon.';
        } else if (progressPercentage >= 75) {
            statusClass = 'status-warning';
            message = 'Getting close to your limit.';
        } else if (progressPercentage >= 50) {
            statusClass = 'status-warning';
            message = 'Halfway through your daily limit.';
        } else {
            statusClass = 'status-safe';
            message = 'All good! Stay mindful of your usage.';
        }

        // Remove all status classes
        statusIndicator.className = 'status-indicator ' + statusClass;
        statusText.textContent = message;
    }

    // Listen for updates from the background script
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === "updateTimeSpent") {
            currentTimeSpent = request.timeSpent;
            updateDisplay();
        }
    });

    // Update display every second when popup is open
    const updateInterval = setInterval(() => {
        chrome.storage.sync.get(['timeSpent'], (result) => {
            if (result.timeSpent !== currentTimeSpent) {
                currentTimeSpent = result.timeSpent || 0;
                updateDisplay();
            }
        });
    }, 1000);

    // Clean up interval when popup closes
    window.addEventListener('beforeunload', () => {
        clearInterval(updateInterval);
    });
});