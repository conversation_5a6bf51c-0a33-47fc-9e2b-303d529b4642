document.addEventListener('DOMContentLoaded', () => {
    // UI Elements
    const currentSiteSection = document.getElementById('currentSiteSection');
    const addSiteSection = document.getElementById('addSiteSection');
    const managedSites = document.getElementById('managedSites');
    const sitesList = document.getElementById('sitesList');

    // Current site elements
    const currentSiteName = document.getElementById('currentSiteName');
    const currentSiteUrl = document.getElementById('currentSiteUrl');
    const timeSpentElement = document.getElementById('timeSpent');
    const timeRemainingElement = document.getElementById('timeRemaining');
    const timeLimitInput = document.getElementById('timeLimit');
    const saveButton = document.getElementById('save');
    const progressFill = document.getElementById('progressFill');
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');
    const quickButtons = document.querySelectorAll('.quick-btn');

    // Add site elements
    const websiteInput = document.getElementById('websiteInput');
    const newSiteTimeLimit = document.getElementById('newSiteTimeLimit');
    const addSiteButton = document.getElementById('addSiteButton');
    const detectCurrentSite = document.getElementById('detectCurrentSite');
    const newSiteQuickButtons = document.querySelectorAll('#addSiteSection .quick-btn');

    let currentDomain = null;
    let currentSiteData = null;
    let allSitesData = {};

    // Initialize the popup
    initializePopup();

    async function initializePopup() {
        // Get current site info from background script
        chrome.runtime.sendMessage({ action: "getCurrentSite" }, (response) => {
            if (response && response.domain) {
                currentDomain = response.domain;
                currentSiteName.textContent = response.siteName;
                currentSiteUrl.textContent = response.domain;
                currentSiteSection.style.display = 'block';
            } else {
                currentSiteSection.style.display = 'none';
            }

            // Load all sites data
            loadAllSitesData();
        });
    }

    function loadAllSitesData() {
        chrome.storage.sync.get(['siteUsage'], (result) => {
            allSitesData = result.siteUsage || {};

            if (currentDomain && allSitesData[currentDomain]) {
                currentSiteData = allSitesData[currentDomain];
                updateCurrentSiteDisplay();
            }

            updateManagedSitesList();
        });
    }

    function updateCurrentSiteDisplay() {
        if (!currentSiteData) return;

        const timeSpent = currentSiteData.timeSpent || 0;
        const timeLimit = currentSiteData.timeLimit || 30;

        timeLimitInput.value = timeLimit;
        updateDisplay(timeSpent, timeLimit);
        updateQuickButtonsState(timeLimit);
    }

    // Event Listeners

    // Current site quick buttons
    quickButtons.forEach(button => {
        button.addEventListener('click', () => {
            const time = parseInt(button.dataset.time);
            timeLimitInput.value = time;
            updateQuickButtonsState(time);
        });
    });

    // New site quick buttons
    newSiteQuickButtons.forEach(button => {
        button.addEventListener('click', () => {
            const time = parseInt(button.dataset.time);
            newSiteTimeLimit.value = time;
            updateNewSiteQuickButtonsState(time);
        });
    });

    // Save current site time limit
    saveButton.addEventListener('click', () => {
        const newLimit = parseInt(timeLimitInput.value);
        if (newLimit > 0 && newLimit <= 1440 && currentDomain) {
            chrome.storage.sync.get(['siteUsage'], (result) => {
                const siteUsage = result.siteUsage || {};
                if (siteUsage[currentDomain]) {
                    siteUsage[currentDomain].timeLimit = newLimit;
                    chrome.storage.sync.set({ siteUsage: siteUsage }, () => {
                        showNotification('Time limit updated successfully!', 'success');
                        currentSiteData = siteUsage[currentDomain];
                        updateCurrentSiteDisplay();
                    });
                }
            });
        } else {
            showNotification('Please enter a valid time limit (1-1440 minutes)', 'error');
        }
    });

    // Detect current site button
    detectCurrentSite.addEventListener('click', () => {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0] && tabs[0].url) {
                const domain = getDomainFromUrl(tabs[0].url);
                if (domain) {
                    websiteInput.value = domain;
                } else {
                    showNotification('Cannot detect domain from current tab', 'error');
                }
            }
        });
    });

    // Add new site button
    addSiteButton.addEventListener('click', () => {
        const domain = cleanDomain(websiteInput.value);
        const timeLimit = parseInt(newSiteTimeLimit.value);

        if (!domain) {
            showNotification('Please enter a valid website domain', 'error');
            return;
        }

        if (!timeLimit || timeLimit < 1 || timeLimit > 1440) {
            showNotification('Please enter a valid time limit (1-1440 minutes)', 'error');
            return;
        }

        chrome.runtime.sendMessage({
            action: "addSiteLimit",
            domain: domain,
            timeLimit: timeLimit
        }, (response) => {
            if (response && response.success) {
                showNotification(`Time limit added for ${domain}!`, 'success');
                websiteInput.value = '';
                newSiteTimeLimit.value = '';
                updateNewSiteQuickButtonsState(null);
                loadAllSitesData();
            } else {
                showNotification('Failed to add website limit', 'error');
            }
        });
    });

    // Utility Functions

    function getDomainFromUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname.replace('www.', '');
        } catch (e) {
            return null;
        }
    }

    function cleanDomain(input) {
        if (!input) return null;

        // Remove protocol if present
        input = input.replace(/^https?:\/\//, '');
        // Remove www. if present
        input = input.replace(/^www\./, '');
        // Remove path if present
        input = input.split('/')[0];
        // Remove port if present
        input = input.split(':')[0];

        return input.toLowerCase().trim();
    }

    function getSiteIcon(domain) {
        const icons = {
            'facebook.com': '📘',
            'instagram.com': '📷',
            'twitter.com': '🐦',
            'x.com': '❌',
            'youtube.com': '📺',
            'tiktok.com': '🎵',
            'linkedin.com': '💼',
            'reddit.com': '🤖',
            'pinterest.com': '📌',
            'snapchat.com': '👻',
            'discord.com': '🎮',
            'twitch.tv': '🎮'
        };
        return icons[domain] || '🌐';
    }

    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    function updateDisplay(timeSpent = 0, timeLimit = 30) {
        const spentSeconds = timeSpent;
        const limitSeconds = timeLimit * 60;
        const remainingSeconds = Math.max(0, limitSeconds - spentSeconds);

        // Update time displays
        timeSpentElement.textContent = formatTime(spentSeconds);
        timeRemainingElement.textContent = formatTime(remainingSeconds);

        // Update progress bar
        const progressPercentage = Math.min(100, (spentSeconds / limitSeconds) * 100);
        progressFill.style.width = `${progressPercentage}%`;

        // Update status indicator
        updateStatusIndicator(progressPercentage, remainingSeconds);

        // Update progress bar color based on usage
        if (progressPercentage >= 90) {
            progressFill.style.background = 'linear-gradient(90deg, #ff6b6b 0%, #ee5a52 100%)';
        } else if (progressPercentage >= 75) {
            progressFill.style.background = 'linear-gradient(90deg, #ffa726 0%, #ff9800 100%)';
        } else {
            progressFill.style.background = 'linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)';
        }
    }

    function updateQuickButtonsState(activeTime) {
        quickButtons.forEach(button => {
            const time = parseInt(button.dataset.time);
            if (time === activeTime) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    }

    function updateNewSiteQuickButtonsState(activeTime) {
        newSiteQuickButtons.forEach(button => {
            const time = parseInt(button.dataset.time);
            if (time === activeTime) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    }

    function updateStatusIndicator(progressPercentage, remainingSeconds) {
        let statusClass, message;

        if (progressPercentage >= 100) {
            statusClass = 'status-danger';
            message = 'Time limit exceeded! Take a break.';
        } else if (progressPercentage >= 90) {
            statusClass = 'status-danger';
            message = 'Almost out of time! Wrap up soon.';
        } else if (progressPercentage >= 75) {
            statusClass = 'status-warning';
            message = 'Getting close to your limit.';
        } else if (progressPercentage >= 50) {
            statusClass = 'status-warning';
            message = 'Halfway through your daily limit.';
        } else {
            statusClass = 'status-safe';
            message = 'All good! Stay mindful of your usage.';
        }

        statusIndicator.className = 'status-indicator ' + statusClass;
        statusText.textContent = message;
    }

    function updateManagedSitesList() {
        const sites = Object.keys(allSitesData);

        if (sites.length === 0) {
            sitesList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">🌐</div>
                    <div class="empty-state-text">
                        No websites are being tracked yet.<br>
                        Add a website above to start monitoring your usage.
                    </div>
                </div>
            `;
            return;
        }

        sitesList.innerHTML = sites.map(domain => {
            const site = allSitesData[domain];
            const timeSpent = formatTime(site.timeSpent || 0);
            const timeLimit = site.timeLimit || 30;
            const progressPercentage = Math.min(100, ((site.timeSpent || 0) / (timeLimit * 60)) * 100);

            return `
                <div class="site-item">
                    <div class="site-item-info">
                        <div class="site-item-icon">${getSiteIcon(domain)}</div>
                        <div class="site-item-details">
                            <div class="site-item-name">${site.siteName || domain}</div>
                            <div class="site-item-stats">${timeSpent} / ${timeLimit}m (${Math.round(progressPercentage)}%)</div>
                        </div>
                    </div>
                    <div class="site-item-actions">
                        <button class="site-action-btn" onclick="editSite('${domain}')">Edit</button>
                        <button class="site-action-btn danger" onclick="removeSite('${domain}')">Remove</button>
                    </div>
                </div>
            `;
        }).join('');
    }

    // Global functions for site management
    window.editSite = function(domain) {
        const site = allSitesData[domain];
        if (site) {
            websiteInput.value = domain;
            newSiteTimeLimit.value = site.timeLimit;
            updateNewSiteQuickButtonsState(site.timeLimit);
            websiteInput.scrollIntoView({ behavior: 'smooth' });
        }
    };

    window.removeSite = function(domain) {
        if (confirm(`Remove time limit for ${domain}?`)) {
            chrome.storage.sync.get(['siteUsage'], (result) => {
                const siteUsage = result.siteUsage || {};
                delete siteUsage[domain];
                chrome.storage.sync.set({ siteUsage: siteUsage }, () => {
                    showNotification(`Removed time limit for ${domain}`, 'success');
                    loadAllSitesData();
                });
            });
        }
    };

    function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                to { opacity: 1; transform: translateX(-50%) translateY(0); }
            }
        `;
        document.head.appendChild(style);

        setTimeout(() => {
            notification.remove();
            style.remove();
        }, 3000);
    }

    // Listen for updates from the background script
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === "updateTimeSpent") {
            if (currentDomain === request.currentDomain) {
                updateDisplay(request.timeSpent, request.timeLimit);
            }

            // Update the managed sites list with new data
            if (request.allSites) {
                allSitesData = request.allSites;
                updateManagedSitesList();
            }
        }
    });

    // Update display every second when popup is open
    const updateInterval = setInterval(() => {
        if (currentDomain) {
            chrome.storage.sync.get(['siteUsage'], (result) => {
                const siteUsage = result.siteUsage || {};
                if (siteUsage[currentDomain]) {
                    const newData = siteUsage[currentDomain];
                    if (newData.timeSpent !== (currentSiteData?.timeSpent || 0)) {
                        currentSiteData = newData;
                        updateCurrentSiteDisplay();
                    }
                }

                // Update all sites data
                allSitesData = siteUsage;
                updateManagedSitesList();
            });
        }
    }, 1000);

    // Clean up interval when popup closes
    window.addEventListener('beforeunload', () => {
        clearInterval(updateInterval);
    });
});