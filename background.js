let activeTabId = null;
let activeTimer = null;
let currentDomain = null;

// Helper function to extract domain from URL
function getDomainFromUrl(url) {
    if (!url) return null;
    try {
        const urlObj = new URL(url);
        return urlObj.hostname.replace('www.', '');
    } catch (e) {
        return null;
    }
}

// Helper function to get a clean site name for display
function getSiteNameFromDomain(domain) {
    if (!domain) return 'Unknown Site';

    // Common site mappings
    const siteNames = {
        'facebook.com': 'Facebook',
        'instagram.com': 'Instagram',
        'twitter.com': 'Twitter',
        'x.com': 'X (Twitter)',
        'youtube.com': 'YouTube',
        'tiktok.com': 'TikTok',
        'linkedin.com': 'LinkedIn',
        'reddit.com': 'Reddit',
        'pinterest.com': 'Pinterest',
        'snapchat.com': 'Snapchat',
        'discord.com': 'Discord',
        'twitch.tv': 'Twitch'
    };

    return siteNames[domain] || domain.charAt(0).toUpperCase() + domain.slice(1);
}

function resetDailyUsage() {
    const today = new Date().toDateString();
    chrome.storage.sync.get(['lastResetDay'], (result) => {
        if (result.lastResetDay !== today) {
            // Reset all site usage for the new day
            chrome.storage.sync.set({
                siteUsage: {},
                lastResetDay: today
            });
        }
    });
}

chrome.runtime.onInstalled.addListener(() => {
    chrome.storage.sync.set({
        siteUsage: {}, // Object to store usage per site: { 'domain.com': { timeSpent: 0, timeLimit: 30 } }
        lastResetDay: new Date().toDateString(),
        defaultTimeLimit: 30 // Default 30 minutes for new sites
    });
});

chrome.tabs.onActivated.addListener((activeInfo) => {
    chrome.tabs.get(activeInfo.tabId, (tab) => {
        const domain = getDomainFromUrl(tab.url);
        if (domain) {
            chrome.storage.sync.get(['siteUsage'], (result) => {
                const siteUsage = result.siteUsage || {};
                if (isTrackableWebsiteSync(domain, siteUsage)) {
                    activeTabId = tab.id;
                    currentDomain = domain;
                    resetDailyUsage();
                    startActiveTimer(domain);
                } else {
                    activeTabId = null;
                    currentDomain = null;
                    stopActiveTimer();
                }
            });
        } else {
            activeTabId = null;
            currentDomain = null;
            stopActiveTimer();
        }
    });
});

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        const domain = getDomainFromUrl(tab.url);
        if (domain) {
            chrome.storage.sync.get(['siteUsage'], (result) => {
                const siteUsage = result.siteUsage || {};
                if (isTrackableWebsiteSync(domain, siteUsage)) {
                    activeTabId = tabId;
                    currentDomain = domain;
                    resetDailyUsage();
                    startActiveTimer(domain);
                } else if (activeTabId === tabId) {
                    // Tab changed to non-trackable site
                    activeTabId = null;
                    currentDomain = null;
                    stopActiveTimer();
                }
            });
        } else if (activeTabId === tabId) {
            // Tab changed to non-trackable site
            activeTabId = null;
            currentDomain = null;
            stopActiveTimer();
        }
    }
});

// Check if a website should be tracked (has a time limit set)
function isTrackableWebsite(domain) {
    return new Promise((resolve) => {
        chrome.storage.sync.get(['siteUsage'], (result) => {
            const siteUsage = result.siteUsage || {};
            resolve(siteUsage.hasOwnProperty(domain));
        });
    });
}

// Synchronous version for immediate checks
function isTrackableWebsiteSync(domain, siteUsage) {
    return siteUsage && siteUsage.hasOwnProperty(domain);
}

function startActiveTimer(domain) {
    if (!activeTimer && domain) {
        activeTimer = setInterval(() => {
            chrome.storage.sync.get(['siteUsage'], (result) => {
                const siteUsage = result.siteUsage || {};

                if (!siteUsage[domain]) {
                    // Site not being tracked, stop timer
                    stopActiveTimer();
                    return;
                }

                const currentSite = siteUsage[domain];
                const newTimeSpent = (currentSite.timeSpent || 0) + 1;

                // Update the site's time spent
                siteUsage[domain] = {
                    ...currentSite,
                    timeSpent: newTimeSpent
                };

                chrome.storage.sync.set({ siteUsage: siteUsage });
                updatePopup(); // Update the popup every second

                // Check if time limit exceeded
                const timeLimit = currentSite.timeLimit || 30; // Default 30 minutes
                if (newTimeSpent >= timeLimit * 60) {
                    if (activeTabId) {
                        chrome.tabs.sendMessage(activeTabId, {
                            action: "showOverlay",
                            siteName: getSiteNameFromDomain(domain)
                        });
                    }
                    stopActiveTimer();
                }
            });
        }, 1000);
    }
}

function stopActiveTimer() {
    if (activeTimer) {
        clearInterval(activeTimer);
        activeTimer = null;
    }
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "leaveWebsite") {
        chrome.tabs.create({ url: 'about:newtab' }, () => {
            if (sender.tab && sender.tab.id) {
                chrome.tabs.remove(sender.tab.id);
            }
        });
        stopActiveTimer();
    } else if (request.action === "setActive") {
        if (sender.tab && sender.tab.id === activeTabId) {
            if (request.isActive) {
                startActiveTimer(currentDomain);
            } else {
                stopActiveTimer();
            }
        }
    } else if (request.action === "pageLoaded") {
        if (sender.tab && sender.tab.url) {
            const domain = getDomainFromUrl(sender.tab.url);
            chrome.storage.sync.get(['siteUsage'], (result) => {
                const siteUsage = result.siteUsage || {};
                if (domain && isTrackableWebsiteSync(domain, siteUsage)) {
                    activeTabId = sender.tab.id;
                    currentDomain = domain;
                    resetDailyUsage();
                    startActiveTimer(domain);
                }
            });
        }
    } else if (request.action === "getCurrentSite") {
        // Return current site info to popup
        sendResponse({
            domain: currentDomain,
            siteName: currentDomain ? getSiteNameFromDomain(currentDomain) : null
        });
    } else if (request.action === "addSiteLimit") {
        // Add a new site to track
        const { domain, timeLimit } = request;
        if (domain && timeLimit > 0) {
            chrome.storage.sync.get(['siteUsage'], (result) => {
                const siteUsage = result.siteUsage || {};
                siteUsage[domain] = {
                    timeSpent: 0,
                    timeLimit: timeLimit,
                    siteName: getSiteNameFromDomain(domain)
                };
                chrome.storage.sync.set({ siteUsage: siteUsage }, () => {
                    sendResponse({ success: true });
                });
            });
            return true; // Keep message channel open for async response
        }
    }
});


function updatePopup() {
    chrome.storage.sync.get(['siteUsage'], (result) => {
        const siteUsage = result.siteUsage || {};
        const currentSiteData = currentDomain ? siteUsage[currentDomain] : null;

        chrome.runtime.sendMessage({
            action: "updateTimeSpent",
            timeSpent: currentSiteData ? currentSiteData.timeSpent || 0 : 0,
            timeLimit: currentSiteData ? currentSiteData.timeLimit || 30 : 30,
            currentDomain: currentDomain,
            siteName: currentDomain ? getSiteNameFromDomain(currentDomain) : null,
            allSites: siteUsage
        });
    });
}

chrome.action.onClicked.addListener(() => {
    updatePopup();
});

// Check and reset daily usage every hour
setInterval(resetDailyUsage, 3600000);