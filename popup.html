<!DOCTYPE html>
<html>

<head>
    <title>Safe Time - Facebook Usage Limiter</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 380px;
            min-height: 500px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            background: white;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .content {
            flex: 1;
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .time-display {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .time-circle {
            width: 120px;
            height: 120px;
            margin: 0 auto 15px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #4facfe 0%, #00f2fe 100%);
            padding: 4px;
        }

        .time-circle-inner {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .time-spent {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            line-height: 1;
        }

        .time-label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .time-remaining {
            font-size: 16px;
            color: #666;
            margin-top: 10px;
        }

        .time-remaining .value {
            font-weight: bold;
            color: #333;
        }

        .controls {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .time-input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: white;
        }

        .time-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .unit-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .save-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .save-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }

        .save-button:active {
            transform: translateY(0);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            margin-top: 15px;
        }

        .status-safe {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .quick-btn {
            flex: 1;
            padding: 8px 12px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-btn:hover {
            border-color: #4facfe;
            color: #4facfe;
        }

        .quick-btn.active {
            background: #4facfe;
            border-color: #4facfe;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Safe Time</h1>
            <p>Facebook Usage Monitor</p>
        </div>

        <div class="content">
            <div class="time-display">
                <div class="time-circle">
                    <div class="time-circle-inner">
                        <div class="time-spent" id="timeSpent">0:00</div>
                        <div class="time-label">spent today</div>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="time-remaining">
                    Time remaining: <span class="value" id="timeRemaining">0:00</span>
                </div>
                <div class="status-indicator" id="statusIndicator">
                    <div class="status-dot"></div>
                    <span id="statusText">All good! Stay mindful.</span>
                </div>
            </div>

            <div class="controls">
                <div class="control-group">
                    <label class="control-label">Daily Time Limit</label>
                    <div class="input-group">
                        <input type="number" class="time-input" id="timeLimit" min="1" max="1440" placeholder="30">
                        <span class="unit-label">minutes</span>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="quick-btn" data-time="15">15m</button>
                    <button class="quick-btn" data-time="30">30m</button>
                    <button class="quick-btn" data-time="60">1h</button>
                    <button class="quick-btn" data-time="120">2h</button>
                </div>

                <button class="save-button" id="save">Update Limit</button>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>

</html>