<!DOCTYPE html>
<html>
  <head>
    <title>Safe Time</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        width: 380px;
        min-height: 500px;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        overflow: hidden;
      }

      .container {
        background: white;
        margin: 0;
        padding: 0;
        height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header h1 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 5px;
      }

      .header p {
        font-size: 14px;
        opacity: 0.9;
      }

      .content {
        flex: 1;
        padding: 25px;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .time-display {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .time-circle {
        width: 120px;
        height: 120px;
        margin: 0 auto 15px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: conic-gradient(from 0deg, #4facfe 0%, #00f2fe 100%);
        padding: 4px;
      }

      .time-circle-inner {
        width: 100%;
        height: 100%;
        background: white;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .time-spent {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        line-height: 1;
      }

      .time-label {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin: 15px 0;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
        border-radius: 4px;
        transition: width 0.3s ease;
        width: 0%;
      }

      .time-remaining {
        font-size: 16px;
        color: #666;
        margin-top: 10px;
      }

      .time-remaining .value {
        font-weight: bold;
        color: #333;
      }

      .controls {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .control-group {
        margin-bottom: 15px;
      }

      .control-label {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: block;
      }

      .input-group {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .time-input {
        flex: 1;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
        background: white;
      }

      .time-input:focus {
        outline: none;
        border-color: #4facfe;
        box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
      }

      .unit-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }

      .save-button {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border: none;
        border-radius: 10px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
      }

      .save-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
      }

      .save-button:active {
        transform: translateY(0);
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 15px;
        border-radius: 8px;
        font-size: 14px;
        margin-top: 15px;
      }

      .status-safe {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status-warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }

      .status-danger {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: currentColor;
      }

      .quick-actions {
        display: flex;
        gap: 10px;
        margin-top: 15px;
      }

      .quick-btn {
        flex: 1;
        padding: 8px 12px;
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .quick-btn:hover {
        border-color: #4facfe;
        color: #4facfe;
      }

      .quick-btn.active {
        background: #4facfe;
        border-color: #4facfe;
        color: white;
      }

      /* New Website Management Styles */
      .current-site {
        margin-bottom: 20px;
      }

      .site-header {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .site-info {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .site-icon {
        font-size: 24px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .site-details {
        flex: 1;
      }

      .site-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
      }

      .site-url {
        font-size: 12px;
        color: #666;
      }

      .add-site-section {
        margin-bottom: 20px;
      }

      .add-site-header {
        text-align: center;
        margin-bottom: 20px;
      }

      .add-site-header h3 {
        font-size: 18px;
        color: #333;
        margin-bottom: 5px;
      }

      .add-site-description {
        font-size: 14px;
        color: #666;
        margin: 0;
      }

      .add-site-form {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .detect-btn {
        padding: 8px 12px;
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
      }

      .detect-btn:hover {
        border-color: #4facfe;
        color: #4facfe;
      }

      .managed-sites {
        margin-top: 20px;
      }

      .section-header {
        margin-bottom: 15px;
      }

      .section-header h3 {
        font-size: 16px;
        color: #333;
        margin-bottom: 5px;
      }

      .section-description {
        font-size: 12px;
        color: #666;
        margin: 0;
      }

      .sites-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .site-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .site-item-info {
        display: flex;
        align-items: center;
        gap: 10px;
        flex: 1;
      }

      .site-item-icon {
        font-size: 16px;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border-radius: 6px;
      }

      .site-item-details {
        flex: 1;
      }

      .site-item-name {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
      }

      .site-item-stats {
        font-size: 11px;
        color: #666;
      }

      .site-item-actions {
        display: flex;
        gap: 5px;
      }

      .site-action-btn {
        padding: 4px 8px;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        font-size: 10px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .site-action-btn:hover {
        border-color: #4facfe;
        color: #4facfe;
      }

      .site-action-btn.danger:hover {
        border-color: #dc3545;
        color: #dc3545;
      }

      .empty-state {
        text-align: center;
        padding: 30px 20px;
        color: #666;
      }

      .empty-state-icon {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.5;
      }

      .empty-state-text {
        font-size: 14px;
        line-height: 1.5;
      }
    </style>
  </head>

  <body>
    <div class="container">
      <div class="header">
        <h1>Safe Time</h1>
        <p>
          Block distracting websites, boost focus, and manage screen time with
          customizable schedules.
        </p>
      </div>

      <div class="content">
        <!-- Current Website Section -->
        <div class="current-site" id="currentSiteSection" style="display: none">
          <div class="site-header">
            <div class="site-info">
              <div class="site-icon">🌐</div>
              <div class="site-details">
                <div class="site-name" id="currentSiteName">
                  Current Website
                </div>
                <div class="site-url" id="currentSiteUrl">example.com</div>
              </div>
            </div>
          </div>

          <div class="time-display">
            <div class="time-circle">
              <div class="time-circle-inner">
                <div class="time-spent" id="timeSpent">0:00</div>
                <div class="time-label">spent today</div>
              </div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="time-remaining">
              Time remaining: <span class="value" id="timeRemaining">0:00</span>
            </div>
            <div class="status-indicator" id="statusIndicator">
              <div class="status-dot"></div>
              <span id="statusText">All good! Stay mindful.</span>
            </div>
          </div>

          <div class="controls">
            <div class="control-group">
              <label class="control-label">Daily Time Limit</label>
              <div class="input-group">
                <input
                  type="number"
                  class="time-input"
                  id="timeLimit"
                  min="1"
                  max="1440"
                  placeholder="30"
                />
                <span class="unit-label">minutes</span>
              </div>
            </div>

            <div class="quick-actions">
              <button class="quick-btn" data-time="15">15m</button>
              <button class="quick-btn" data-time="30">30m</button>
              <button class="quick-btn" data-time="60">1h</button>
              <button class="quick-btn" data-time="120">2h</button>
            </div>

            <button class="save-button" id="save">Update Limit</button>
          </div>
        </div>

        <!-- Add New Website Section -->
        <div class="add-site-section" id="addSiteSection">
          <div class="add-site-header">
            <h3>Add Website Time Limit</h3>
            <p class="add-site-description">
              Set a daily time limit for any website
            </p>
          </div>

          <div class="add-site-form">
            <div class="control-group">
              <label class="control-label">Website URL or Domain</label>
              <div class="input-group">
                <input
                  type="text"
                  class="time-input"
                  id="websiteInput"
                  placeholder="instagram.com"
                />
                <button class="detect-btn" id="detectCurrentSite">
                  Current Site
                </button>
              </div>
            </div>

            <div class="control-group">
              <label class="control-label">Daily Time Limit</label>
              <div class="input-group">
                <input
                  type="number"
                  class="time-input"
                  id="newSiteTimeLimit"
                  min="1"
                  max="1440"
                  placeholder="30"
                />
                <span class="unit-label">minutes</span>
              </div>
            </div>

            <div class="quick-actions">
              <button class="quick-btn" data-time="15">15m</button>
              <button class="quick-btn" data-time="30">30m</button>
              <button class="quick-btn" data-time="60">1h</button>
              <button class="quick-btn" data-time="120">2h</button>
            </div>

            <button class="save-button" id="addSiteButton">
              Add Website Limit
            </button>
          </div>
        </div>

        <!-- Managed Websites List -->
        <div class="managed-sites" id="managedSites">
          <div class="section-header">
            <h3>Managed Websites</h3>
            <p class="section-description">Websites with active time limits</p>
          </div>
          <div class="sites-list" id="sitesList">
            <!-- Sites will be populated by JavaScript -->
          </div>
        </div>
      </div>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
